<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Modal - Settings <PERSON><PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #6366f1;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background: #4f46e5;
        }
        .test-button.secondary {
            background: #64748b;
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        .modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .modal-content {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
        }
        .modal-close {
            background: none;
            border: none;
            font-size: 1.25rem;
            color: #64748b;
            cursor: pointer;
            padding: 0.25rem;
        }
        .modal-body {
            padding: 1.5rem;
        }
        .modal-footer {
            padding: 1.5rem;
            border-top: 1px solid #e2e8f0;
            text-align: right;
        }
        .setting-group {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
            padding: 1rem;
            background: #f8fafc;
            border-radius: 6px;
        }
        .toggle {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }
        .toggle input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #cbd5e1;
            transition: all 0.3s ease;
            border-radius: 24px;
        }
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: all 0.3s ease;
            border-radius: 50%;
        }
        .toggle input:checked + .toggle-slider {
            background-color: #6366f1;
        }
        .toggle input:checked + .toggle-slider:before {
            transform: translateX(20px);
        }
        .log {
            background: #1e293b;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 6px;
            font-weight: bold;
        }
        .status.success {
            background: #d1fae5;
            color: #065f46;
        }
        .status.error {
            background: #fee2e2;
            color: #991b1b;
        }
        .status.info {
            background: #dbeafe;
            color: #1e40af;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔧 Debug Modal - Settings Button</h1>
        
        <div class="status info" id="status">
            Ready to test...
        </div>

        <h2>Test Buttons</h2>
        <button class="test-button" onclick="testShowSettings()">Test showSettings()</button>
        <button class="test-button" onclick="testOpenModal()">Test openModal('settingsModal')</button>
        <button class="test-button" onclick="testDirectModal()">Test Direct Modal Show</button>
        <button class="test-button secondary" onclick="clearLog()">Clear Log</button>

        <h2>Debug Information</h2>
        <div class="log" id="debugLog"></div>

        <h2>Manual Tests</h2>
        <button class="test-button" onclick="checkElements()">Check DOM Elements</button>
        <button class="test-button" onclick="checkFunctions()">Check Functions</button>
        <button class="test-button" onclick="checkCSS()">Check CSS Classes</button>
        <button class="test-button" onclick="simulateClick()">Simulate Button Click</button>
    </div>

    <!-- Settings Modal -->
    <div id="settingsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>App Settings</h3>
                <button class="modal-close" onclick="closeModal('settingsModal')">
                    ×
                </button>
            </div>
            <div class="modal-body">
                <div class="setting-group">
                    <label>API Key Storage</label>
                    <label class="toggle">
                        <input type="checkbox" id="saveApiKey" checked>
                        <span class="toggle-slider"></span>
                    </label>
                    <small>Save API key locally (browser storage)</small>
                </div>
                <div class="setting-group">
                    <label>Auto-cleanup uploads</label>
                    <label class="toggle">
                        <input type="checkbox" id="autoCleanup" checked>
                        <span class="toggle-slider"></span>
                    </label>
                    <small>Automatically delete uploaded files after processing</small>
                </div>
            </div>
            <div class="modal-footer">
                <button class="test-button" onclick="saveSettings()">Save</button>
            </div>
        </div>
    </div>

    <script>
        // Debug logging
        function log(message, type = 'info') {
            const logDiv = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logDiv.textContent += logEntry;
            logDiv.scrollTop = logDiv.scrollHeight;
            
            // Update status
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        function clearLog() {
            document.getElementById('debugLog').textContent = '';
            log('Log cleared', 'info');
        }

        // Modal Functions (copied from main script)
        function showSettings() {
            log('showSettings() called', 'info');
            try {
                openModal('settingsModal');
                log('showSettings() completed successfully', 'success');
            } catch (error) {
                log(`showSettings() error: ${error.message}`, 'error');
            }
        }

        function openModal(modalId) {
            log(`openModal('${modalId}') called`, 'info');
            try {
                const modal = document.getElementById(modalId);
                if (!modal) {
                    throw new Error(`Modal with id '${modalId}' not found`);
                }
                
                log(`Modal element found: ${modal.tagName}`, 'info');
                
                modal.classList.add('active');
                document.body.style.overflow = 'hidden';
                
                log(`Modal classes: ${modal.className}`, 'info');
                log('Modal opened successfully', 'success');
            } catch (error) {
                log(`openModal() error: ${error.message}`, 'error');
            }
        }

        function closeModal(modalId) {
            log(`closeModal('${modalId}') called`, 'info');
            try {
                const modal = document.getElementById(modalId);
                if (modal) {
                    modal.classList.remove('active');
                    document.body.style.overflow = '';
                    log('Modal closed successfully', 'success');
                } else {
                    log(`Modal '${modalId}' not found`, 'error');
                }
            } catch (error) {
                log(`closeModal() error: ${error.message}`, 'error');
            }
        }

        function saveSettings() {
            log('saveSettings() called', 'info');
            closeModal('settingsModal');
            log('Settings saved and modal closed', 'success');
        }

        // Test Functions
        function testShowSettings() {
            log('=== Testing showSettings() ===', 'info');
            showSettings();
        }

        function testOpenModal() {
            log('=== Testing openModal() directly ===', 'info');
            openModal('settingsModal');
        }

        function testDirectModal() {
            log('=== Testing direct modal manipulation ===', 'info');
            try {
                const modal = document.getElementById('settingsModal');
                modal.style.display = 'flex';
                modal.style.alignItems = 'center';
                modal.style.justifyContent = 'center';
                log('Direct modal show successful', 'success');
            } catch (error) {
                log(`Direct modal error: ${error.message}`, 'error');
            }
        }

        function checkElements() {
            log('=== Checking DOM Elements ===', 'info');
            
            const elements = [
                'settingsModal',
                'saveApiKey',
                'autoCleanup'
            ];
            
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    log(`✅ Element '${id}' found: ${element.tagName}`, 'success');
                } else {
                    log(`❌ Element '${id}' NOT found`, 'error');
                }
            });
        }

        function checkFunctions() {
            log('=== Checking Functions ===', 'info');
            
            const functions = [
                'showSettings',
                'openModal',
                'closeModal',
                'saveSettings'
            ];
            
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    log(`✅ Function '${funcName}' exists`, 'success');
                } else {
                    log(`❌ Function '${funcName}' NOT found`, 'error');
                }
            });
        }

        function checkCSS() {
            log('=== Checking CSS Classes ===', 'info');
            
            const modal = document.getElementById('settingsModal');
            if (modal) {
                const computedStyle = window.getComputedStyle(modal);
                log(`Modal display: ${computedStyle.display}`, 'info');
                log(`Modal position: ${computedStyle.position}`, 'info');
                log(`Modal z-index: ${computedStyle.zIndex}`, 'info');
                
                // Test adding active class
                modal.classList.add('active');
                const activeStyle = window.getComputedStyle(modal);
                log(`Modal display with .active: ${activeStyle.display}`, 'info');
                modal.classList.remove('active');
            }
        }

        function simulateClick() {
            log('=== Simulating Button Click ===', 'info');
            
            // Create a fake button like in the main page
            const fakeButton = document.createElement('button');
            fakeButton.className = 'btn btn-primary';
            fakeButton.onclick = showSettings;
            fakeButton.innerHTML = '<i class="fas fa-cog"></i> Settings';
            
            log('Fake button created, triggering click...', 'info');
            fakeButton.click();
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('Debug page loaded successfully', 'success');
            log('Click any test button to start debugging', 'info');
        });

        // Click outside to close modal
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal')) {
                closeModal(e.target.id);
            }
        });

        // ESC key to close modal
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const activeModal = document.querySelector('.modal.active');
                if (activeModal) {
                    closeModal(activeModal.id);
                }
            }
        });
    </script>
</body>
</html>
