const express = require('express');
const path = require('path');
const RunwareService = require('../services/runware');

const router = express.Router();

// Job storage (in production, use Redis or database)
const jobs = new Map();

// Middleware to check API key
const validateApiKey = (req, res, next) => {
  const apiKey = req.body.apiKey || req.headers['x-api-key'] || process.env.RUNWARE_API_KEY;

  if (!apiKey) {
    return res.status(401).json({
      error: 'API key required',
      message: 'Please provide your Runware API key'
    });
  }

  req.runwareApiKey = apiKey;
  next();
};

// POST /api/process/single - Process single image
router.post('/single', validateApiKey, async (req, res) => {
  try {
    const {
      imageUrl,
      filename,
      backgroundStyle = 'minimalist',
      upscaleFactor = 2,
      generateCount = 4,
      finalPolish = false
    } = req.body;

    // Validate input
    if (!imageUrl && !filename) {
      return res.status(400).json({
        error: 'Missing image input',
        message: 'Provide either imageUrl or filename'
      });
    }

    // Determine image input
    let imageInput;
    if (imageUrl) {
      imageInput = imageUrl;
    } else {
      imageInput = path.join(__dirname, '../uploads/input', filename);
    }

    console.log(`🎯 Starting processing for: ${imageUrl || filename}`);

    // Create job entry
    const jobId = require('uuid').v4();
    jobs.set(jobId, {
      id: jobId,
      status: 'processing',
      startTime: Date.now(),
      input: { imageUrl, filename },
      options: { backgroundStyle, upscaleFactor, generateCount, finalPolish }
    });

    // Start processing (don't wait for completion)
    processImageAsync(jobId, imageInput, {
      backgroundStyle,
      upscaleFactor: parseInt(upscaleFactor),
      generateCount: parseInt(generateCount),
      finalPolish: Boolean(finalPolish)
    }, req.runwareApiKey);

    res.json({
      success: true,
      message: 'Processing started',
      jobId,
      estimatedTime: '30-60 seconds',
      checkStatus: `/api/status/${jobId}`
    });

  } catch (error) {
    console.error('Process error:', error);
    res.status(500).json({
      error: 'Processing failed to start',
      message: error.message
    });
  }
});

// POST /api/process/batch - Process multiple images
router.post('/batch', validateApiKey, async (req, res) => {
  try {
    const {
      images, // Array of {imageUrl?, filename?}
      backgroundStyle = 'minimalist',
      upscaleFactor = 2,
      generateCount = 4,
      finalPolish = false
    } = req.body;

    if (!images || !Array.isArray(images) || images.length === 0) {
      return res.status(400).json({
        error: 'Invalid images input',
        message: 'Provide an array of image objects'
      });
    }

    if (images.length > 10) {
      return res.status(400).json({
        error: 'Too many images',
        message: 'Maximum 10 images per batch'
      });
    }

    const batchId = require('uuid').v4();
    console.log(`🎯 Starting batch processing: ${images.length} images`);

    // Create batch job entry
    jobs.set(batchId, {
      id: batchId,
      type: 'batch',
      status: 'processing',
      startTime: Date.now(),
      input: { images },
      options: { backgroundStyle, upscaleFactor, generateCount, finalPolish },
      progress: {
        total: images.length,
        completed: 0,
        current: 0
      }
    });

    // Start batch processing
    processBatchAsync(batchId, images, {
      backgroundStyle,
      upscaleFactor: parseInt(upscaleFactor),
      generateCount: parseInt(generateCount),
      finalPolish: Boolean(finalPolish)
    }, req.runwareApiKey);

    res.json({
      success: true,
      message: 'Batch processing started',
      batchId,
      imageCount: images.length,
      estimatedTime: `${Math.ceil(images.length * 45 / 60)} minutes`,
      checkStatus: `/api/status/${batchId}`
    });

  } catch (error) {
    console.error('Batch process error:', error);
    res.status(500).json({
      error: 'Batch processing failed to start',
      message: error.message
    });
  }
});

// GET /api/process/styles - Get available background styles
router.get('/styles', (req, res) => {
  res.json({
    success: true,
    styles: RunwareService.getBackgroundStyles()
  });
});

// POST /api/process/test - Test API connection
router.post('/test', validateApiKey, async (req, res) => {
  try {
    const runware = new RunwareService(req.runwareApiKey);

    // Test with a simple API call (you might want to implement a ping endpoint)
    // For now, we'll just validate the service can be created
    res.json({
      success: true,
      message: 'API key is valid and service is ready',
      apiKeyPrefix: req.runwareApiKey.substring(0, 8) + '...',
      availableStyles: Object.keys(RunwareService.getBackgroundStyles())
    });

  } catch (error) {
    res.status(401).json({
      error: 'API key validation failed',
      message: error.message
    });
  }
});

// Async processing function for single image
async function processImageAsync(jobId, imageInput, options, apiKey) {
  const job = jobs.get(jobId);
  if (!job) return;

  try {
    const runware = new RunwareService(apiKey);

    // Update job status
    job.status = 'processing';
    job.currentStep = 'Background Removal';

    const result = await runware.processProduct(imageInput, {
      ...options,
      verbose: true
    });

    // Update job with results
    job.status = result.success ? 'completed' : 'failed';
    job.result = result;
    job.endTime = Date.now();
    job.processingTime = job.endTime - job.startTime;

    console.log(`✅ Job ${jobId} ${job.status} in ${(job.processingTime / 1000).toFixed(2)}s`);

  } catch (error) {
    console.error(`❌ Job ${jobId} failed:`, error);
    job.status = 'failed';
    job.error = error.message;
    job.endTime = Date.now();
    job.processingTime = job.endTime - job.startTime;
  }
}

// Async processing function for batch
async function processBatchAsync(batchId, images, options, apiKey) {
  const job = jobs.get(batchId);
  if (!job) return;

  try {
    const runware = new RunwareService(apiKey);
    const imageInputs = images.map(img => {
      if (img.imageUrl) return img.imageUrl;
      if (img.filename) return path.join(__dirname, '../uploads/input', img.filename);
      throw new Error('Invalid image input');
    });

    // Update progress callback
    const originalProcessProduct = runware.processProduct.bind(runware);
    runware.processProduct = async (imageInput, opts) => {
      job.progress.current++;
      job.currentStep = `Processing image ${job.progress.current}/${job.progress.total}`;

      const result = await originalProcessProduct(imageInput, opts);

      job.progress.completed++;
      return result;
    };

    const result = await runware.processBatch(imageInputs, {
      ...options,
      verbose: false
    });

    // Update job with results
    job.status = 'completed';
    job.result = result;
    job.endTime = Date.now();
    job.processingTime = job.endTime - job.startTime;

    console.log(`✅ Batch ${batchId} completed: ${result.summary.successful}/${result.summary.total} successful`);

  } catch (error) {
    console.error(`❌ Batch ${batchId} failed:`, error);
    job.status = 'failed';
    job.error = error.message;
    job.endTime = Date.now();
    job.processingTime = job.endTime - job.startTime;
  }
}

// POST /api/process/cancel/:jobId - Cancel processing job
router.post('/cancel/:jobId', (req, res) => {
  try {
    const jobId = req.params.jobId;
    const job = jobs.get(jobId);

    if (!job) {
      return res.status(404).json({
        error: 'Job not found',
        jobId
      });
    }

    if (job.status === 'completed' || job.status === 'failed') {
      return res.status(400).json({
        error: 'Job already finished',
        status: job.status,
        jobId
      });
    }

    // Mark job as cancelled
    job.status = 'cancelled';
    job.endTime = Date.now();
    job.processingTime = job.endTime - job.startTime;

    console.log(`🛑 Job ${jobId} cancelled`);

    res.json({
      success: true,
      message: 'Job cancelled successfully',
      jobId,
      status: 'cancelled'
    });

  } catch (error) {
    console.error('Cancel job error:', error);
    res.status(500).json({
      error: 'Failed to cancel job',
      message: error.message
    });
  }
});

// Auto-cleanup old jobs
setInterval(() => {
  const maxAge = 2 * 60 * 60 * 1000; // 2 hours
  const cutoff = Date.now() - maxAge;

  for (const [jobId, job] of jobs.entries()) {
    if (job.startTime < cutoff) {
      jobs.delete(jobId);
    }
  }
}, 60 * 60 * 1000); // Run every hour

module.exports = router;
module.exports.jobs = jobs; // Export for status route